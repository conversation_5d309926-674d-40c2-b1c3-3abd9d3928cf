package team.aikero.murmuration.service.node.shared

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonTypeInfo
import org.babyfish.jimmer.sql.ast.tuple.Tuple4
import org.babyfish.jimmer.sql.kt.KSqlClient
import team.aikero.blade.util.spring.Springs
import team.aikero.blade.util.spring.bean
import team.aikero.murmuration.service.node.task.aigc_image.FloralPrintTemplateDetail
import team.aikero.murmuration.service.node.task.aigc_image.GraphicStorehouse
import java.net.URI

/**
 * 图片
 *
 * 子类型由 SealedClassSubtypesDetectModule 自动注册
 * <AUTHOR>
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.DEDUCTION, defaultImpl = SimpleImage::class)
sealed interface Image {

    /**
     * 获取图片URL
     */
    @JsonIgnore
    fun getUrl(): String

    /**
     * 将图片转换为URI
     */
    fun toURI(): URI {
        return URI.create(getUrl())
    }
}

/**
 * 简单图片
 */
data class SimpleImage(
    @field:JsonAlias("url", "imageUrl", "image")
    val imageUrl: String
) : Image {
    override fun getUrl() = imageUrl
}

/**
 * 图案库图片(仅图案)
 */
data class GraphicImage(val imageId: Long) : Image {

    private val lazyImage = lazy { bean<KSqlClient>().findOneById(GraphicStorehouse::class, imageId) }

    /**
     * 图案库图片
     */
    @JsonIgnore
    val image: GraphicStorehouse= lazyImage.value

    /**
     * 获取图片URL
     */
    override fun getUrl(): String {
        return image.imageUrl
    }
}

/**
 * 版型图(无图案/无人)
 */
data class TemplateImage(
    /**
     * 版型明细ID
     */
    val templateDetailId: Long
) : Image {
    override fun getUrl(): String {
        val sql = Springs.get<KSqlClient>()
        val templateDetail = sql.findOneById(FloralPrintTemplateDetail::class, templateDetailId)
        return templateDetail.imageUrl
    }
}

/**
 * 款式图
 */
data class StyleImage(
    /**
     * 图片URL
     */
    val imageUrl: String,

    /**
     * 图案库图片
     */
    val graphicImage: GraphicImage,

    /**
     * 版型图
     */
    val templateImage: TemplateImage,
) : Image {
    override fun getUrl() = imageUrl
}

/**
 * 款式图列表按照图套分组
 */
fun List<StyleImage>.groupByKit(): List<List<StyleImage>> {
    val sql = Springs.get<KSqlClient>()

    // templateDetailId -> templateDetail
    val templateDetailIds = this.map { it.templateImage.templateDetailId }
    val templateDetailMap = sql
        .findByIds(FloralPrintTemplateDetail::class, templateDetailIds)
        .associateBy { it.templateDetailId }

    // 按照图套分组，再聚合
    val groupByKit = this.groupBy { image ->
        val templateDetailId = image.templateImage.templateDetailId
        val templateDetail = templateDetailMap[templateDetailId] ?: throw IllegalStateException("版型明细[$templateDetailId]不存在")
        Tuple4(
            templateDetail.mappingAreaCode,
            templateDetail.kitName,
            templateDetail.kitType,
            templateDetail.marketingAtlasCode,
        )
    }
    return groupByKit.values.toList()
}
