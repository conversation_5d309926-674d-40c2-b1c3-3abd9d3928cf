package team.aikero.murmuration.service.node.task.aip.upscale

import team.aikero.murmuration.common.enums.task.Resolution
import team.aikero.murmuration.common.req.task.UpScaleRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.NodeIdentifier
import team.aikero.murmuration.core.workflow.context.WorkflowNodeContext
import team.aikero.murmuration.core.workflow.node.builtin.SimpleTaskResult
import team.aikero.murmuration.core.workflow.node.param.Parameter
import team.aikero.murmuration.core.workflow.task.readTaskStorage
import team.aikero.murmuration.service.node.shared.GraphicImage
import team.aikero.murmuration.service.node.shared.ImageListInput
import team.aikero.murmuration.service.node.shared.ImageListOutput
import team.aikero.murmuration.service.node.shared.node.SmartGraphicStorehouseTaskNode
import team.aikero.murmuration.service.node.task.aigc_image.GraphicStorehouseType

/**
 * 超分任务节点
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@NodeIdentifier(name = "超分", supplier = Supplier.REALESRGAN, ability = Ability.UPSCALE)
class UpScaleTaskNode : SmartGraphicStorehouseTaskNode<ImageListInput, UpScaleNodeParameter, ImageListOutput>() {

    override fun createTask(context: WorkflowNodeContext, input: ImageListInput, parameter: UpScaleNodeParameter) {
        val graphicImages = input.images.toGraphicImage()

        // 循环创建超分任务
        for (image in graphicImages) {
            taskManager.createTask(
                nodeInstanceId = context.node.id,
                supplier = Supplier.REALESRGAN,
                ability = Ability.UPSCALE,
                request = UpScaleRequest(
                    imageUrl = image.getUrl(),
                    targetResolution = parameter.targetResolution,
                ),
                storage = image
            )
        }
    }

    override fun assembleOutput(context: WorkflowNodeContext, taskResults: List<SimpleTaskResult>): ImageListOutput {
        val storage = taskManager.readTaskStorage<GraphicImage>(context.node.id)

        val graphicImages = taskResults.map {
            val originImage = storage[it.taskId] ?: throw IllegalStateException("找不到任务[${it.taskId}]对应的关系")
            detectAndConvert(it.url, GraphicStorehouseType.ENLARGE_IMAGE, originImage)
        }.saveToGraphicImage()

        return ImageListOutput(graphicImages)
    }
}

data class UpScaleNodeParameter(
    /**
     * 目标分辨率
     */
    val targetResolution: Resolution = Resolution.R_2K,
) : Parameter
