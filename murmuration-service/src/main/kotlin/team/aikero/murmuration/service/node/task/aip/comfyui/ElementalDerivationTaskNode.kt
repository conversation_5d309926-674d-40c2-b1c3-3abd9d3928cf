package team.aikero.murmuration.service.node.task.aip.comfyui

import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import team.aikero.murmuration.common.req.task.ElementalDerivationRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.NodeIdentifier
import team.aikero.murmuration.core.annotations.NodeProperties
import team.aikero.murmuration.core.workflow.context.WorkflowNodeContext
import team.aikero.murmuration.core.workflow.node.builtin.SimpleTaskResult
import team.aikero.murmuration.core.workflow.node.param.Parameter
import team.aikero.murmuration.core.workflow.task.readTaskStorage
import team.aikero.murmuration.service.node.shared.GraphicImage
import team.aikero.murmuration.service.node.shared.ImageListInput
import team.aikero.murmuration.service.node.shared.ImageListOutput
import team.aikero.murmuration.service.node.shared.node.SmartGraphicStorehouseTaskNode
import team.aikero.murmuration.service.node.task.aigc_image.GraphicStorehouseType

/**
 * 元素化衍生任务节点
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@NodeIdentifier(name = "元素化衍生", supplier = Supplier.COMFY_UI, ability = Ability.IMAGE_DERIVATION)
class ElementalDerivationTaskNode(): SmartGraphicStorehouseTaskNode<ImageListInput, ElementalDerivationParameter, ImageListOutput>() {

    override fun createTask(context: WorkflowNodeContext, input: ImageListInput, parameter: ElementalDerivationParameter) {
        val graphicImages = input.images.toGraphicImage()
        // 循环创建元素化衍生任务
        for (image in graphicImages) {
            taskManager.createTask(
                nodeInstanceId = context.node.id,
                supplier = Supplier.COMFY_UI,
                ability = Ability.IMAGE_DERIVATION,
                request = ElementalDerivationRequest(
                    imageUrl = image.getUrl(),
                    n = parameter.n,
                    checkSensitiveImage = true,
                ),
                storage = image
            )
        }
    }

    override fun assembleOutput(context: WorkflowNodeContext, taskResults: List<SimpleTaskResult>): ImageListOutput {
        val storage = taskManager.readTaskStorage<GraphicImage>(context.node.id)
        val graphicImages = taskResults.map {
            val image = storage[it.taskId] ?: throw IllegalStateException("找不到任务[${it.taskId}]对应的关系")
            detectAndConvert(it.url, GraphicStorehouseType.RESULT_IMAGE, image)
        }.saveToGraphicImage()

        return ImageListOutput(graphicImages)
    }
}

data class ElementalDerivationParameter(
    /**
     * 生图数量
     */
    @NodeProperties(name = "生图数量", value = "1")
    @field:Max(8)
    @field:Min(1)
    val n: Int = 4,
): Parameter
