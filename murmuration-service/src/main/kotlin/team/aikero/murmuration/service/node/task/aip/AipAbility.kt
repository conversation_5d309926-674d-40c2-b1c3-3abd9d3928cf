package team.aikero.murmuration.service.node.task.aip

/**
 * 算法调度平台能力
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
enum class AipAbility(val taskType: String, val modelName: String) {

    /**
     * 超分（无损）
     */
    UPSCALE("UPscale", "realesrgan"),

    /**
     * COMFY_UI
     */
    COMFY_UI("comfyui", "comfyui"),

    /**
     * COMFY_UI_GPT
     */
    COMFY_UI_GPT("comfyui", "comfyui_gpt"),

    /**
     * 花型3.0-logo提取
     */
    PATTERN_EXTRACTION_V3("pattern_extraction_v3", "pattern30"),
}
