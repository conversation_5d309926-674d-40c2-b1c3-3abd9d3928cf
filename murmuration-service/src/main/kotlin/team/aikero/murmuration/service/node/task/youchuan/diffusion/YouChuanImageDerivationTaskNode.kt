package team.aikero.murmuration.service.node.task.youchuan.diffusion

import jakarta.validation.constraints.Size
import team.aikero.murmuration.common.req.task.YouChuanDiffusionRequest
import team.aikero.murmuration.core.Ability
import team.aikero.murmuration.core.Supplier
import team.aikero.murmuration.core.annotations.NodeIdentifier
import team.aikero.murmuration.core.annotations.NodeProperties
import team.aikero.murmuration.core.workflow.context.WorkflowNodeContext
import team.aikero.murmuration.core.workflow.node.builtin.SimpleTaskResult
import team.aikero.murmuration.core.workflow.node.param.Input
import team.aikero.murmuration.core.workflow.node.param.Parameter
import team.aikero.murmuration.core.workflow.task.readTaskStorage
import team.aikero.murmuration.service.node.shared.GraphicImage
import team.aikero.murmuration.service.node.shared.Image
import team.aikero.murmuration.service.node.shared.ImageListOutput
import team.aikero.murmuration.service.node.shared.node.SmartGraphicStorehouseTaskNode
import team.aikero.murmuration.service.node.task.aigc_image.GraphicStorehouseType

/**
 * 悠船图案衍生任务节点
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@NodeIdentifier(name = "悠船-图案衍生", supplier = Supplier.MIDJOURNEY, ability = Ability.IMAGE_DERIVATION)
class YouChuanImageDerivationTaskNode: SmartGraphicStorehouseTaskNode<YouChuanImageDerivationInput, YouChuanImageDerivationParameter, ImageListOutput>() {

    override fun createTask(context: WorkflowNodeContext, input: YouChuanImageDerivationInput, parameter: YouChuanImageDerivationParameter) {
        check(parameter.n % 4 == 0) { "生图数量必须是4的倍数" }

        val graphicImages = input.images.toGraphicImage()
        val bottomImageUrls = input.bottomImages?.map { it.getUrl() }
        val styleImageUrls = input.referenceStyleImages?.map { it.getUrl() }
        val roleImageUrls = input.referenceRoleImages?.map { it.getUrl() }


        // 循环创建任务
        repeat(parameter.n / 4) {
            for (image in graphicImages) {
                taskManager.createTask(
                    nodeInstanceId = context.node.id,
                    supplier = Supplier.MIDJOURNEY,
                    ability = Ability.IMAGE_DERIVATION,
                    request = YouChuanDiffusionRequest(
                        prompt = input.prompt,
                        referenceImageUrl = image.getUrl(),
                        bottomImageUrls = bottomImageUrls,
                        referenceStyleImageUrls = styleImageUrls,
                        referenceRoleImageUrls = roleImageUrls,
                        moodboardId = parameter.moodboardId,
                    ),
                    storage = image,
                )
            }
        }
    }

    override fun assembleOutput(context: WorkflowNodeContext, taskResults: List<SimpleTaskResult>): ImageListOutput {
        val storage = taskManager.readTaskStorage<GraphicImage>(context.node.id)

        val graphicImages = taskResults.map {
            val originImage = storage[it.taskId] ?: throw IllegalStateException("找不到任务[${it.taskId}]对应的关系")
            detectAndConvert(it.url, GraphicStorehouseType.RESULT_IMAGE, originImage)
        }.saveToGraphicImage()

        return ImageListOutput(graphicImages)
    }
}

data class YouChuanImageDerivationInput(
    @NodeProperties("提示词")
    val prompt: String,

    @NodeProperties(name = "底图列表（来源素材选择）")
    @field:Size(min = 1)
    val images: List<Image>,

    @NodeProperties(name = "底图列表")
    val bottomImages: List<Image>?,

    @NodeProperties(name = "参照风格图列表")
    val referenceStyleImages: List<Image>?,

    // 版本=6时为参照角色图，版本=7时为参照万物图
    @NodeProperties(name = "参照角色/万物图列表")
    val referenceRoleImages: List<Image>?,
): Input

data class YouChuanImageDerivationParameter(
    @NodeProperties("生图数量")
    val n: Int = 4,

    @NodeProperties("风格ID")
    val moodboardId: String? = null,
): Parameter
