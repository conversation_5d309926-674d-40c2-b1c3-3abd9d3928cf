package team.aikero.murmuration.core.workflow.entity

import org.babyfish.jimmer.sql.*
import team.aikero.blade.data.jimmer.entity.BaseEntity
import team.aikero.blade.data.jimmer.entity.OrgWithFilter
import team.aikero.blade.data.jimmer.entity.TenantId

/**
 * 工作流实例
 *
 * <AUTHOR>
 */
@Entity
interface  WorkflowInstance: BaseEntity, OrgWithFilter, TenantId {

    /**
     * 工作流定义
     */
    @ManyToOne
    val workflowDefinition: WorkflowDefinition

    /**
     * 工作流编号
     *
     * 流水号
     */
    @Key
    val serialNumber :String

    /**
     * 工作流模式
     */
    val mode: WorkflowMode

    /**
     * 工作流运行状态
     */
    val status: WorkflowStatus

    /**
     * 逻辑删除
     */
    @LogicalDeleted("true")
    val deleted: Boolean

    @OneToOne(mappedBy = "workflowInstance")
    val detail: WorkflowInstanceDetail?

    /**
     * 节点实例
     */
    @OneToMany(mappedBy = "workflowInstance", orderedProps = [OrderedProp("nodeDefinitionId")])
    val nodeInstances: List<NodeInstance>
}

/**
 * 工作流状态
 *
 * <AUTHOR>
 */
enum class WorkflowStatus {

    /**
     * 启动
     */
    START,

    /**
     * 运行中
     */
    RUNNING,

    /**
     * 已暂停
     */
    PAUSED,

    /**
     * 完成
     */
    COMPLETED,

    /**
     * 取消
     */
    CANCELLED,

    /**
     * 失败
     */
    FAILED, ;

    /**
     * 是否已完成
     */
    fun isFinished(): Boolean {
        return this == COMPLETED || this == FAILED || this == CANCELLED
    }
}

/**
 * 工作流模式
 *
 * <AUTHOR>
 */
enum class WorkflowMode {
    /**
     * 节点
     */
    NODE,

    /**
     * 工作流
     */
    WORKFLOW
}
