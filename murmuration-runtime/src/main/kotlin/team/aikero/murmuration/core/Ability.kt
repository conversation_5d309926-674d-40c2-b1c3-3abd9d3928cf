package team.aikero.murmuration.core

import team.aikero.murmuration.common.annotation.EnumComment

/**
 * 能力类型
 *
 * 模型功能、接口功能、算法、代码逻辑
 *
 * <AUTHOR>
 */
@EnumComment
enum class Ability {
    /**
     * 开始
     */
    START,

    /**
     * 结束
     */
    END,

    /**
     * 图案衍生
     */
    IMAGE_DERIVATION,

    /**
     * 超分
     */
    UPSCALE,

    /**
     * 模特图衍生
     */
    MODEL_IMAGE_DERIVATION,

    /**
     * 图套贴图
     */
    TEXTURE,

    /**
     * 姿势裂变
     */
    POSTURAL_FISSION,

    /**
     * 抠图
     */
    CUTOUTS,

    /**
     * 换脸
     */
    CHANGE_FACE,

    /**
     * 换背景
     */
    CHANGE_BACKGROUND,

    /**
     * 服装上身
     */
    TRY_ON,

    /**
     * 推送POD选款
     */
    PUSH_POD_STYLE,

    /**
     * 图案提取
     */
    PATTERN_EXTRACTION,
}

fun Ability.getTitle(): String {
    return ""
}
